using RealEstateVisningApp.Database.Model;

namespace RealEstateVisningApp.BusinessLayer;

public class SupabaseKeepAliveService
{
    public async Task KeepAliveAsync()
    {
        var client = SupabaseService.HangfireClient;
        try
        {
            var result = await client
                .From<RealEstateListing>()
                .Limit(1)
                .Get();
            
            // Just to keep the connection alive - we don't need to do anything with the result
            Console.WriteLine($"KeepAlive: Retrieved {result.Models.Count} record(s)");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"KeepAlive failed: {ex.Message}");
            throw;
        }
    }
}