using Microsoft.Extensions.Configuration;
using RealEstateVisningApp.Database.Model;
using Supabase;

namespace RealEstateVisningApp.BusinessLayer;

public class SupabaseService
{
    private readonly Supabase.Client _client;

    public SupabaseService(IConfiguration configuration)
    {
        var url = configuration["Supabase:Url"] ?? throw new Exception("Supabase:Url not set");
        var key = configuration["Supabase:AnonKey"] ?? throw new Exception("Supabase:AnonKey not set");

        var options = new SupabaseOptions { AutoConnectRealtime = false };
        _client = new Supabase.Client(url, key, options);
        _client.InitializeAsync().Wait();
    }

    public Supabase.Client HangfireClient => _client;


    public async Task KeepAliveAsync()
    {
        try
        {
            var result = await HangfireClient
                .From<RealEstateListing>()
                .Limit(1)
                .Get();
            
            // Just to keep the connection alive - we don't need to do anything with the result
            Console.WriteLine($"KeepAlive: Retrieved {result.Models.Count} record(s)");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"KeepAlive failed: {ex.Message}");
        }
    }
}