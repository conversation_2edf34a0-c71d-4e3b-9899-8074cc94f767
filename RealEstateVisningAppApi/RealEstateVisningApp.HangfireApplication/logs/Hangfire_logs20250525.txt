2025-05-25 22:32:13.836 +02:00 [INF] Appsettings Environment Development
2025-05-25 22:32:19.513 +02:00 [INF] Now listening on: http://localhost:5208
2025-05-25 22:32:19.517 +02:00 [INF] Starting Hangfire Server using job storage: 'Hangfire.MemoryStorage.MemoryStorage'
2025-05-25 22:32:19.517 +02:00 [INF] Using the following options for Hangfire Server:
    Worker count: 20
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-05-25 22:32:19.529 +02:00 [DBG] Execution loop BackgroundServerProcess:c90d6a55 has started in 1.7827 ms
2025-05-25 22:32:19.530 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 22:32:19.531 +02:00 [INF] Hosting environment: Development
2025-05-25 22:32:19.531 +02:00 [INF] Content root path: /Users/<USER>/Grunder/RealEstate/RealEstateVisningAppApi/RealEstateVisningApp.HangfireApplication
2025-05-25 22:32:19.541 +02:00 [INF] Server nomll7y29f004:85293:7f639336 successfully announced in 2.7352 ms
2025-05-25 22:32:19.542 +02:00 [DBG] Execution loop ServerHeartbeatProcess:44fac767 has started in 1.1739 ms
2025-05-25 22:32:19.542 +02:00 [INF] Server nomll7y29f004:85293:7f639336 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-05-25 22:32:19.566 +02:00 [DBG] Execution loop ServerWatchdog:b0fec5fe has started in 23.231 ms
2025-05-25 22:32:19.566 +02:00 [DBG] Execution loop ServerJobCancellationWatcher:5c818c69 has started in 23.1643 ms
2025-05-25 22:32:19.590 +02:00 [DBG] Execution loop ExpirationManager:ded4b853 has started in 23.9717 ms
2025-05-25 22:32:19.590 +02:00 [DBG] Execution loop CountersAggregator:56f9c8f0 has started in 23.7864 ms
2025-05-25 22:32:19.616 +02:00 [DBG] Execution loop Worker:4cb1fac7 has started in 49.4558 ms
2025-05-25 22:32:19.616 +02:00 [DBG] Execution loop Worker:162b8320 has started in 49.6184 ms
2025-05-25 22:32:19.642 +02:00 [DBG] Execution loop Worker:92e6105b has started in 75.1966 ms
2025-05-25 22:32:19.668 +02:00 [DBG] Execution loop Worker:58e0fc2e has started in 100.9278 ms
2025-05-25 22:32:19.668 +02:00 [DBG] Execution loop Worker:62be52e3 has started in 101.0557 ms
2025-05-25 22:32:19.668 +02:00 [DBG] Execution loop Worker:3ed0e065 has started in 101.1007 ms
2025-05-25 22:32:19.669 +02:00 [DBG] Execution loop Worker:05648547 has started in 101.9215 ms
2025-05-25 22:32:19.716 +02:00 [DBG] Execution loop Worker:4cc5399f has started in 149.3887 ms
2025-05-25 22:32:19.741 +02:00 [DBG] Execution loop Worker:549b294b has started in 174.0722 ms
2025-05-25 22:32:19.741 +02:00 [DBG] Execution loop Worker:3f6d75e3 has started in 174.0654 ms
2025-05-25 22:32:19.741 +02:00 [DBG] Execution loop Worker:de39416c has started in 174.1994 ms
2025-05-25 22:32:19.766 +02:00 [DBG] Execution loop Worker:7838172c has started in 198.8112 ms
2025-05-25 22:32:19.766 +02:00 [DBG] Execution loop Worker:11396829 has started in 198.8912 ms
2025-05-25 22:32:19.766 +02:00 [DBG] Execution loop Worker:616d6489 has started in 198.8901 ms
2025-05-25 22:32:19.792 +02:00 [DBG] Execution loop Worker:06dcaf58 has started in 225.1375 ms
2025-05-25 22:32:19.793 +02:00 [DBG] Execution loop Worker:c408a21b has started in 225.8312 ms
2025-05-25 22:32:19.818 +02:00 [DBG] Execution loop Worker:c324d3ea has started in 251.4139 ms
2025-05-25 22:32:19.844 +02:00 [DBG] Execution loop Worker:01393d25 has started in 277.0297 ms
2025-05-25 22:32:19.844 +02:00 [DBG] Execution loop Worker:126c4dba has started in 277.3269 ms
2025-05-25 22:32:19.844 +02:00 [DBG] Execution loop Worker:fe6d149f has started in 277.378 ms
2025-05-25 22:32:19.845 +02:00 [INF] Server nomll7y29f004:85293:7f639336 all the dispatchers started
2025-05-25 22:32:19.846 +02:00 [DBG] Execution loop DelayedJobScheduler:c75359a0 has started in 2.5663 ms
2025-05-25 22:32:19.846 +02:00 [DBG] Execution loop RecurringJobScheduler:155d00dc has started in 2.0143 ms
2025-05-25 22:32:22.346 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:32:22.363 +02:00 [WRN] Failed to determine the https port for redirect.
2025-05-25 22:32:22.370 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.411 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.411 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire - 200 null text/html 65.2535ms
2025-05-25 22:32:22.418 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css18140187990185 - null null
2025-05-25 22:32:22.419 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.442 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css-dark18140649655880 - null null
2025-05-25 22:32:22.444 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/js181401799546444 - null null
2025-05-25 22:32:22.444 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.444 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.445 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.446 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css-dark18140649655880 - 200 null text/css 3.7117ms
2025-05-25 22:32:22.515 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.515 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.517 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css18140187990185 - 200 null text/css 98.5825ms
2025-05-25 22:32:22.517 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/js181401799546444 - 200 null application/javascript 73.0408ms
2025-05-25 22:32:22.526 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/fonts/glyphicons-halflings-regular/woff2 - null null
2025-05-25 22:32:22.526 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.527 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:22.527 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/fonts/glyphicons-halflings-regular/woff2 - 200 null font/woff2 1.3691ms
2025-05-25 22:32:24.551 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:32:24.554 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:24.581 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:24.582 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 30.5006ms
2025-05-25 22:32:25.011 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/servers - null null
2025-05-25 22:32:25.012 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:25.043 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:25.043 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/servers - 200 null text/html 32.6222ms
2025-05-25 22:32:25.881 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:32:25.882 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:25.905 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:25.905 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 23.4903ms
2025-05-25 22:32:27.940 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:32:27.941 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:27.946 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:27.947 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 8.3953ms
2025-05-25 22:32:29.422 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - application/x-www-form-urlencoded; charset=UTF-8 30
2025-05-25 22:32:29.422 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:29.448 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:29.448 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - 204 null null 26.6236ms
2025-05-25 22:32:29.457 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:32:29.457 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:29.510 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:29.510 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 53.5048ms
2025-05-25 22:32:30.720 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/jobs/enqueued - null null
2025-05-25 22:32:30.721 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:30.734 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:30.734 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/jobs/enqueued - 200 null text/html 13.579ms
2025-05-25 22:32:31.687 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/jobs/succeeded - null null
2025-05-25 22:32:31.688 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:31.697 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:31.698 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/jobs/succeeded - 200 null text/html 10.4187ms
2025-05-25 22:32:33.719 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-25 22:32:33.720 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:33.720 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:33.720 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.2583ms
2025-05-25 22:32:35.728 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-25 22:32:35.728 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:35.729 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:35.729 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.1698ms
2025-05-25 22:32:37.734 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-25 22:32:37.735 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:37.735 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:37.736 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.4907ms
2025-05-25 22:32:39.741 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-25 22:32:39.742 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:39.742 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:39.743 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0966ms
2025-05-25 22:32:41.751 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-25 22:32:41.752 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:41.753 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:41.754 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.034ms
2025-05-25 22:32:43.763 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 392
2025-05-25 22:32:43.764 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:43.767 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:43.767 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.7265ms
2025-05-25 22:32:43.938 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/jobs/succeeded/requeue - application/x-www-form-urlencoded; charset=UTF-8 47
2025-05-25 22:32:43.939 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:43.947 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:43.947 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/jobs/succeeded/requeue - 204 null null 8.6058ms
2025-05-25 22:32:43.953 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/jobs/succeeded - null null
2025-05-25 22:32:43.953 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:32:43.954 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:32:43.954 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/jobs/succeeded - 200 null text/html 1.4532ms
2025-05-25 22:34:56.787 +02:00 [INF] Appsettings Environment Development
2025-05-25 22:35:00.382 +02:00 [INF] Now listening on: http://localhost:5208
2025-05-25 22:35:00.386 +02:00 [INF] Starting Hangfire Server using job storage: 'Hangfire.MemoryStorage.MemoryStorage'
2025-05-25 22:35:00.387 +02:00 [INF] Using the following options for Hangfire Server:
    Worker count: 20
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-05-25 22:35:00.403 +02:00 [DBG] Execution loop BackgroundServerProcess:080e26cb has started in 2.1289 ms
2025-05-25 22:35:00.403 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 22:35:00.404 +02:00 [INF] Hosting environment: Development
2025-05-25 22:35:00.404 +02:00 [INF] Content root path: /Users/<USER>/Grunder/RealEstate/RealEstateVisningAppApi/RealEstateVisningApp.HangfireApplication
2025-05-25 22:35:00.412 +02:00 [INF] Server nomll7y29f004:86276:f5a42e51 successfully announced in 2.5717 ms
2025-05-25 22:35:00.413 +02:00 [DBG] Execution loop ServerHeartbeatProcess:a275f5c4 has started in 1.1896 ms
2025-05-25 22:35:00.413 +02:00 [INF] Server nomll7y29f004:86276:f5a42e51 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-05-25 22:35:00.440 +02:00 [DBG] Execution loop ServerWatchdog:546175f9 has started in 26.1713 ms
2025-05-25 22:35:00.440 +02:00 [DBG] Execution loop ServerJobCancellationWatcher:c7548ac6 has started in 26.1505 ms
2025-05-25 22:35:00.466 +02:00 [DBG] Execution loop CountersAggregator:630fb4a8 has started in 26.0023 ms
2025-05-25 22:35:00.466 +02:00 [DBG] Execution loop ExpirationManager:ecddd148 has started in 26.1529 ms
2025-05-25 22:35:00.492 +02:00 [DBG] Execution loop Worker:8a2678a9 has started in 51.5977 ms
2025-05-25 22:35:00.517 +02:00 [DBG] Execution loop Worker:50995576 has started in 76.9351 ms
2025-05-25 22:35:00.543 +02:00 [DBG] Execution loop Worker:b2178f39 has started in 102.6596 ms
2025-05-25 22:35:00.569 +02:00 [DBG] Execution loop Worker:601b0775 has started in 127.9915 ms
2025-05-25 22:35:00.569 +02:00 [DBG] Execution loop Worker:7302ac4a has started in 128.0782 ms
2025-05-25 22:35:00.593 +02:00 [DBG] Execution loop Worker:e6cdeea7 has started in 152.868 ms
2025-05-25 22:35:00.594 +02:00 [DBG] Execution loop Worker:f6a77e74 has started in 153.7492 ms
2025-05-25 22:35:00.620 +02:00 [DBG] Execution loop Worker:fc6bbced has started in 179.466 ms
2025-05-25 22:35:00.620 +02:00 [DBG] Execution loop Worker:6261fb80 has started in 179.4622 ms
2025-05-25 22:35:00.620 +02:00 [DBG] Execution loop Worker:6aa03660 has started in 179.4664 ms
2025-05-25 22:35:00.621 +02:00 [DBG] Execution loop Worker:766eb891 has started in 180.4555 ms
2025-05-25 22:35:00.622 +02:00 [DBG] Execution loop Worker:11077b05 has started in 181.3996 ms
2025-05-25 22:35:00.647 +02:00 [DBG] Execution loop Worker:c602fb31 has started in 206.5402 ms
2025-05-25 22:35:00.648 +02:00 [DBG] Execution loop Worker:362d08a4 has started in 207.07 ms
2025-05-25 22:35:00.648 +02:00 [DBG] Execution loop Worker:19159876 has started in 207.1707 ms
2025-05-25 22:35:00.649 +02:00 [DBG] Execution loop Worker:316bc3e6 has started in 208.5996 ms
2025-05-25 22:35:00.701 +02:00 [DBG] Execution loop Worker:b7fa6348 has started in 260.2037 ms
2025-05-25 22:35:00.725 +02:00 [DBG] Execution loop Worker:0b4fb6bb has started in 284.0562 ms
2025-05-25 22:35:00.725 +02:00 [DBG] Execution loop Worker:cb67c33c has started in 284.0933 ms
2025-05-25 22:35:00.701 +02:00 [DBG] Execution loop Worker:e23bdfca has started in 260.2548 ms
2025-05-25 22:35:00.774 +02:00 [INF] Server nomll7y29f004:86276:f5a42e51 all the dispatchers started
2025-05-25 22:35:00.774 +02:00 [DBG] Execution loop DelayedJobScheduler:2b0d7bff has started in 72.831 ms
2025-05-25 22:35:00.774 +02:00 [DBG] Execution loop RecurringJobScheduler:212b44e6 has started in 48.9939 ms
2025-05-25 22:35:00.895 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:35:00.911 +02:00 [DBG] 1 recurring job(s) processed by scheduler.
2025-05-25 22:35:00.916 +02:00 [WRN] Failed to determine the https port for redirect.
2025-05-25 22:35:00.923 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:12.182 +02:00 [INF] Appsettings Environment Development
2025-05-25 22:36:12.571 +02:00 [INF] Now listening on: http://localhost:5208
2025-05-25 22:36:12.575 +02:00 [INF] Starting Hangfire Server using job storage: 'Hangfire.MemoryStorage.MemoryStorage'
2025-05-25 22:36:12.575 +02:00 [INF] Using the following options for Hangfire Server:
    Worker count: 20
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-05-25 22:36:12.589 +02:00 [DBG] Execution loop BackgroundServerProcess:ec6c106d has started in 2.0892 ms
2025-05-25 22:36:12.603 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 22:36:12.603 +02:00 [INF] Hosting environment: Development
2025-05-25 22:36:12.603 +02:00 [INF] Content root path: /Users/<USER>/Grunder/RealEstate/RealEstateVisningAppApi/RealEstateVisningApp.HangfireApplication
2025-05-25 22:36:12.606 +02:00 [INF] Server nomll7y29f004:86754:408db7b2 successfully announced in 3.2117 ms
2025-05-25 22:36:12.607 +02:00 [DBG] Execution loop ServerHeartbeatProcess:9bee4ef2 has started in 0.9634 ms
2025-05-25 22:36:12.607 +02:00 [INF] Server nomll7y29f004:86754:408db7b2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-05-25 22:36:12.608 +02:00 [DBG] Execution loop ServerWatchdog:97c92fc8 has started in 0.6543 ms
2025-05-25 22:36:12.609 +02:00 [DBG] Execution loop ServerJobCancellationWatcher:6dbc6108 has started in 1.144 ms
2025-05-25 22:36:12.630 +02:00 [DBG] Execution loop ExpirationManager:f610762c has started in 21.3315 ms
2025-05-25 22:36:12.630 +02:00 [DBG] Execution loop CountersAggregator:68f16c21 has started in 21.2297 ms
2025-05-25 22:36:12.656 +02:00 [DBG] Execution loop Worker:bdb6ed58 has started in 46.9221 ms
2025-05-25 22:36:12.656 +02:00 [DBG] Execution loop Worker:746276e0 has started in 46.994 ms
2025-05-25 22:36:12.657 +02:00 [DBG] Execution loop Worker:a5afee5d has started in 47.5954 ms
2025-05-25 22:36:12.657 +02:00 [DBG] Execution loop Worker:00ad692e has started in 47.6749 ms
2025-05-25 22:36:12.700 +02:00 [DBG] Execution loop Worker:620d0bbc has started in 90.4252 ms
2025-05-25 22:36:12.700 +02:00 [DBG] Execution loop Worker:963962b0 has started in 90.3965 ms
2025-05-25 22:36:12.725 +02:00 [DBG] Execution loop Worker:447f4c30 has started in 116.0118 ms
2025-05-25 22:36:12.751 +02:00 [DBG] Execution loop Worker:efb418e5 has started in 116.0879 ms
2025-05-25 22:36:12.751 +02:00 [DBG] Execution loop Worker:a6c9d9e9 has started in 141.619 ms
2025-05-25 22:36:12.777 +02:00 [DBG] Execution loop Worker:2cb7108a has started in 167.5346 ms
2025-05-25 22:36:12.802 +02:00 [DBG] Execution loop Worker:79deb1b0 has started in 192.751 ms
2025-05-25 22:36:12.802 +02:00 [DBG] Execution loop Worker:4fd23db8 has started in 192.7656 ms
2025-05-25 22:36:12.802 +02:00 [DBG] Execution loop Worker:e648b388 has started in 192.6648 ms
2025-05-25 22:36:12.826 +02:00 [DBG] Execution loop Worker:4c373921 has started in 216.4701 ms
2025-05-25 22:36:12.852 +02:00 [DBG] Execution loop Worker:2f20e04f has started in 242.0879 ms
2025-05-25 22:36:12.877 +02:00 [DBG] Execution loop Worker:9520a878 has started in 267.6608 ms
2025-05-25 22:36:12.898 +02:00 [DBG] Execution loop Worker:407d33ac has started in 288.4921 ms
2025-05-25 22:36:12.924 +02:00 [DBG] Execution loop Worker:bae9ace6 has started in 314.3273 ms
2025-05-25 22:36:12.924 +02:00 [DBG] Execution loop Worker:10aafa66 has started in 314.3252 ms
2025-05-25 22:36:12.924 +02:00 [DBG] Execution loop Worker:06e44bf8 has started in 314.9032 ms
2025-05-25 22:36:12.926 +02:00 [DBG] Execution loop DelayedJobScheduler:7007397b has started in 1.7197 ms
2025-05-25 22:36:12.926 +02:00 [INF] Server nomll7y29f004:86754:408db7b2 all the dispatchers started
2025-05-25 22:36:12.926 +02:00 [DBG] Execution loop RecurringJobScheduler:92f47e79 has started in 1.9962 ms
2025-05-25 22:36:13.073 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:36:13.092 +02:00 [WRN] Failed to determine the https port for redirect.
2025-05-25 22:36:13.100 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.141 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.141 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire - 200 null text/html 68.5769ms
2025-05-25 22:36:13.148 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css18140108157650 - null null
2025-05-25 22:36:13.149 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.200 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css-dark181401381802507 - null null
2025-05-25 22:36:13.226 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/js181402018516112 - null null
2025-05-25 22:36:13.226 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.226 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.228 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.231 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css-dark181401381802507 - 200 null text/css 30.806ms
2025-05-25 22:36:13.237 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.237 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css18140108157650 - 200 null text/css 88.8967ms
2025-05-25 22:36:13.247 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:13.249 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/js181402018516112 - 200 null application/javascript 22.8463ms
2025-05-25 22:36:15.273 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:15.275 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:15.295 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:15.295 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 21.7244ms
2025-05-25 22:36:15.418 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:36:15.419 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:15.450 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:15.450 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 31.5905ms
2025-05-25 22:36:17.487 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:36:17.491 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:17.494 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:17.494 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 7.0127ms
2025-05-25 22:36:18.781 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - application/x-www-form-urlencoded; charset=UTF-8 30
2025-05-25 22:36:18.782 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:18.828 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:18.828 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - 204 null null 46.9235ms
2025-05-25 22:36:18.834 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:36:18.835 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:18.837 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:18.838 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 3.0377ms
2025-05-25 22:36:22.296 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:36:22.296 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:22.304 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:22.304 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 8.3487ms
2025-05-25 22:36:34.024 +02:00 [INF] Appsettings Environment Development
2025-05-25 22:36:34.429 +02:00 [INF] Now listening on: http://localhost:5208
2025-05-25 22:36:34.433 +02:00 [INF] Starting Hangfire Server using job storage: 'Hangfire.MemoryStorage.MemoryStorage'
2025-05-25 22:36:34.433 +02:00 [INF] Using the following options for Hangfire Server:
    Worker count: 20
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-05-25 22:36:34.447 +02:00 [DBG] Execution loop BackgroundServerProcess:b61679c4 has started in 1.8795 ms
2025-05-25 22:36:34.447 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 22:36:34.447 +02:00 [INF] Hosting environment: Development
2025-05-25 22:36:34.447 +02:00 [INF] Content root path: /Users/<USER>/Grunder/RealEstate/RealEstateVisningAppApi/RealEstateVisningApp.HangfireApplication
2025-05-25 22:36:34.455 +02:00 [INF] Server nomll7y29f004:86927:43134f78 successfully announced in 2.974 ms
2025-05-25 22:36:34.457 +02:00 [DBG] Execution loop ServerHeartbeatProcess:3c0e0097 has started in 1.32 ms
2025-05-25 22:36:34.457 +02:00 [INF] Server nomll7y29f004:86927:43134f78 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-05-25 22:36:34.458 +02:00 [DBG] Execution loop ServerWatchdog:e99e3fe2 has started in 1.1376 ms
2025-05-25 22:36:34.458 +02:00 [DBG] Execution loop ServerJobCancellationWatcher:d3f23ca0 has started in 1.07 ms
2025-05-25 22:36:34.485 +02:00 [DBG] Execution loop ExpirationManager:b384a86b has started in 26.246 ms
2025-05-25 22:36:34.485 +02:00 [DBG] Execution loop CountersAggregator:54cbee75 has started in 26.1789 ms
2025-05-25 22:36:34.486 +02:00 [DBG] Execution loop Worker:9138b05a has started in 26.9725 ms
2025-05-25 22:36:34.508 +02:00 [DBG] Execution loop Worker:04e48d0c has started in 48.5303 ms
2025-05-25 22:36:34.509 +02:00 [DBG] Execution loop Worker:79f6e734 has started in 49.3785 ms
2025-05-25 22:36:34.534 +02:00 [DBG] Execution loop Worker:9e6b7afc has started in 75.0504 ms
2025-05-25 22:36:34.583 +02:00 [DBG] Execution loop Worker:16dda225 has started in 124.1525 ms
2025-05-25 22:36:34.583 +02:00 [DBG] Execution loop Worker:619b6148 has started in 124.1352 ms
2025-05-25 22:36:34.583 +02:00 [DBG] Execution loop Worker:6cc687fd has started in 124.0917 ms
2025-05-25 22:36:34.605 +02:00 [DBG] Execution loop Worker:6d023d18 has started in 145.9448 ms
2025-05-25 22:36:34.605 +02:00 [DBG] Execution loop Worker:a97a7c46 has started in 146.009 ms
2025-05-25 22:36:34.630 +02:00 [DBG] Execution loop Worker:a139befa has started in 170.9427 ms
2025-05-25 22:36:34.656 +02:00 [DBG] Execution loop Worker:5e560d70 has started in 196.3954 ms
2025-05-25 22:36:34.656 +02:00 [DBG] Execution loop Worker:7faf17ab has started in 196.4278 ms
2025-05-25 22:36:34.681 +02:00 [DBG] Execution loop Worker:d6f4e7f7 has started in 222.1095 ms
2025-05-25 22:36:34.682 +02:00 [DBG] Execution loop Worker:3e3681d6 has started in 222.8714 ms
2025-05-25 22:36:34.682 +02:00 [DBG] Execution loop Worker:d11ea311 has started in 222.9113 ms
2025-05-25 22:36:34.709 +02:00 [DBG] Execution loop Worker:f8109dd6 has started in 250.2065 ms
2025-05-25 22:36:34.735 +02:00 [DBG] Execution loop Worker:82bed782 has started in 275.429 ms
2025-05-25 22:36:34.735 +02:00 [DBG] Execution loop Worker:a254d309 has started in 275.5107 ms
2025-05-25 22:36:34.736 +02:00 [DBG] Execution loop Worker:3368edb5 has started in 276.6807 ms
2025-05-25 22:36:34.735 +02:00 [DBG] Execution loop Worker:3d865c0d has started in 275.5468 ms
2025-05-25 22:36:34.737 +02:00 [INF] Server nomll7y29f004:86927:43134f78 all the dispatchers started
2025-05-25 22:36:34.759 +02:00 [DBG] Execution loop RecurringJobScheduler:53d0d92a has started in 23.3954 ms
2025-05-25 22:36:34.759 +02:00 [DBG] Execution loop DelayedJobScheduler:220ebd24 has started in 24.5118 ms
2025-05-25 22:36:34.891 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:36:34.907 +02:00 [WRN] Failed to determine the https port for redirect.
2025-05-25 22:36:34.914 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:34.952 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:34.953 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire - 200 null text/html 61.9615ms
2025-05-25 22:36:35.009 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/js18140442849503 - null null
2025-05-25 22:36:35.010 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css-dark18140206222696 - null null
2025-05-25 22:36:35.010 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css181401073407758 - null null
2025-05-25 22:36:35.011 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:35.011 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:35.011 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:35.013 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:35.015 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css-dark18140206222696 - 200 null text/css 5.1904ms
2025-05-25 22:36:35.023 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:35.023 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css181401073407758 - 200 null text/css 13.318ms
2025-05-25 22:36:35.032 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:35.055 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/js18140442849503 - 200 null application/javascript 45.9094ms
2025-05-25 22:36:38.070 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:38.074 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:38.096 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:38.096 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 26.1756ms
2025-05-25 22:36:41.051 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:41.052 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:41.056 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:41.057 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.7123ms
2025-05-25 22:36:44.047 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:44.048 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:44.048 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:44.048 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.977ms
2025-05-25 22:36:47.051 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:47.052 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:47.053 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:47.054 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 3.1207ms
2025-05-25 22:36:50.049 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:50.051 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:50.052 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:50.053 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.4238ms
2025-05-25 22:36:53.050 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:53.051 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:53.053 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:53.056 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 5.5117ms
2025-05-25 22:36:56.049 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:56.050 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:56.050 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:56.051 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.5841ms
2025-05-25 22:36:59.046 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:36:59.046 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:36:59.047 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:36:59.047 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.44ms
2025-05-25 22:37:01.055 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 258
2025-05-25 22:37:01.055 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:37:01.056 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:37:01.056 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 1.0296ms
2025-05-25 22:37:01.198 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:37:01.199 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:37:01.199 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:37:01.200 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire - 200 null text/html 1.6166ms
2025-05-25 22:37:02.659 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:37:02.660 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:37:02.691 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:37:02.692 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 32.5493ms
2025-05-25 22:37:04.607 +02:00 [DBG] Server nomll7y29f004:86927:43134f78 heartbeat successfully sent
2025-05-25 22:37:04.726 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:37:04.727 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:37:04.728 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:37:04.729 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 2.3842ms
2025-05-25 22:37:05.094 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - application/x-www-form-urlencoded; charset=UTF-8 30
2025-05-25 22:37:05.094 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:37:05.131 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:37:05.131 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - 204 null null 37.3896ms
2025-05-25 22:37:05.140 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:37:05.141 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:37:05.143 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:37:05.143 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 2.3756ms
2025-05-25 22:43:00.704 +02:00 [INF] Appsettings Environment Development
2025-05-25 22:43:03.396 +02:00 [INF] Now listening on: http://localhost:5208
2025-05-25 22:43:03.399 +02:00 [INF] Starting Hangfire Server using job storage: 'Hangfire.MemoryStorage.MemoryStorage'
2025-05-25 22:43:03.400 +02:00 [INF] Using the following options for Hangfire Server:
    Worker count: 20
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-05-25 22:43:03.414 +02:00 [DBG] Execution loop BackgroundServerProcess:547d4a46 has started in 1.7756 ms
2025-05-25 22:43:03.414 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 22:43:03.414 +02:00 [INF] Hosting environment: Development
2025-05-25 22:43:03.414 +02:00 [INF] Content root path: /Users/<USER>/Grunder/RealEstate/RealEstateVisningAppApi/RealEstateVisningApp.HangfireApplication
2025-05-25 22:43:03.423 +02:00 [INF] Server nomll7y29f004:89075:fc9a2a1a successfully announced in 3.1358 ms
2025-05-25 22:43:03.425 +02:00 [DBG] Execution loop ServerHeartbeatProcess:5125ee20 has started in 1.2318 ms
2025-05-25 22:43:03.425 +02:00 [INF] Server nomll7y29f004:89075:fc9a2a1a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-05-25 22:43:03.452 +02:00 [DBG] Execution loop ServerWatchdog:24fe4336 has started in 26.2585 ms
2025-05-25 22:43:03.452 +02:00 [DBG] Execution loop ServerJobCancellationWatcher:c7b50d0e has started in 26.1849 ms
2025-05-25 22:43:03.476 +02:00 [DBG] Execution loop ExpirationManager:ed3857c7 has started in 23.8948 ms
2025-05-25 22:43:03.476 +02:00 [DBG] Execution loop CountersAggregator:2d34528c has started in 23.6641 ms
2025-05-25 22:43:03.500 +02:00 [DBG] Execution loop Worker:fb01a6d9 has started in 47.3778 ms
2025-05-25 22:43:03.500 +02:00 [DBG] Execution loop Worker:338e8cdb has started in 47.445 ms
2025-05-25 22:43:03.500 +02:00 [DBG] Execution loop Worker:159b06ae has started in 47.4888 ms
2025-05-25 22:43:03.502 +02:00 [DBG] Execution loop Worker:2e4dba5f has started in 48.8952 ms
2025-05-25 22:43:03.527 +02:00 [DBG] Execution loop Worker:6c3f6d8b has started in 74.4728 ms
2025-05-25 22:43:03.550 +02:00 [DBG] Execution loop Worker:e36b94d9 has started in 96.9169 ms
2025-05-25 22:43:03.576 +02:00 [DBG] Execution loop Worker:3d21c1a7 has started in 122.7363 ms
2025-05-25 22:43:03.576 +02:00 [DBG] Execution loop Worker:9e6f757e has started in 123.3053 ms
2025-05-25 22:43:03.602 +02:00 [DBG] Execution loop Worker:6df8b6a0 has started in 148.7871 ms
2025-05-25 22:43:03.627 +02:00 [DBG] Execution loop Worker:53467fd6 has started in 174.3256 ms
2025-05-25 22:43:03.651 +02:00 [DBG] Execution loop Worker:9ede1ee9 has started in 198.1012 ms
2025-05-25 22:43:03.676 +02:00 [DBG] Execution loop Worker:d9d81102 has started in 223.1812 ms
2025-05-25 22:43:03.676 +02:00 [DBG] Execution loop Worker:30596606 has started in 223.2872 ms
2025-05-25 22:43:03.676 +02:00 [DBG] Execution loop Worker:0e074fb1 has started in 223.3146 ms
2025-05-25 22:43:03.703 +02:00 [DBG] Execution loop Worker:aade38f1 has started in 249.8296 ms
2025-05-25 22:43:03.703 +02:00 [DBG] Execution loop Worker:643ddbe2 has started in 249.9534 ms
2025-05-25 22:43:03.703 +02:00 [DBG] Execution loop Worker:e5cfe001 has started in 249.9365 ms
2025-05-25 22:43:03.730 +02:00 [DBG] Execution loop Worker:d81eeae4 has started in 276.8275 ms
2025-05-25 22:43:03.755 +02:00 [DBG] Execution loop Worker:c50c2335 has started in 302.4599 ms
2025-05-25 22:43:03.755 +02:00 [DBG] Execution loop Worker:2fa6501b has started in 302.6491 ms
2025-05-25 22:43:03.756 +02:00 [DBG] Execution loop DelayedJobScheduler:a3acc81b has started in 26.841 ms
2025-05-25 22:43:03.757 +02:00 [INF] Server nomll7y29f004:89075:fc9a2a1a all the dispatchers started
2025-05-25 22:43:03.758 +02:00 [DBG] Execution loop RecurringJobScheduler:9e18ef5e has started in 1.812 ms
2025-05-25 22:43:03.917 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:43:03.932 +02:00 [WRN] Failed to determine the https port for redirect.
2025-05-25 22:43:03.938 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:03.976 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:03.977 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire - 200 null text/html 60.5246ms
2025-05-25 22:43:04.011 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/js18140479805672 - null null
2025-05-25 22:43:04.011 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css-dark18140456242501 - null null
2025-05-25 22:43:04.011 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css18140428600601 - null null
2025-05-25 22:43:04.012 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:04.012 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:04.012 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:04.014 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:04.016 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css-dark18140456242501 - 200 null text/css 4.8148ms
2025-05-25 22:43:04.031 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:04.031 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:04.033 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css18140428600601 - 200 null text/css 21.4177ms
2025-05-25 22:43:04.033 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/js18140479805672 - 200 null application/javascript 22.0526ms
2025-05-25 22:43:05.387 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:43:05.389 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:05.420 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:05.420 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 32.5244ms
2025-05-25 22:43:07.461 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:43:07.463 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:07.478 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:07.478 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 16.7932ms
2025-05-25 22:43:08.070 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - application/x-www-form-urlencoded; charset=UTF-8 30
2025-05-25 22:43:08.071 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:08.113 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:08.113 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - 204 null null 43.5465ms
2025-05-25 22:43:08.120 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:43:08.121 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:43:08.122 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:43:08.122 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 1.9654ms
2025-05-25 22:43:59.474 +02:00 [INF] Appsettings Environment Development
2025-05-25 22:44:07.729 +02:00 [INF] Now listening on: http://localhost:5208
2025-05-25 22:44:07.733 +02:00 [INF] Starting Hangfire Server using job storage: 'Hangfire.MemoryStorage.MemoryStorage'
2025-05-25 22:44:07.733 +02:00 [INF] Using the following options for Hangfire Server:
    Worker count: 20
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-05-25 22:44:07.747 +02:00 [DBG] Execution loop BackgroundServerProcess:350e6a95 has started in 1.8313 ms
2025-05-25 22:44:07.748 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 22:44:07.748 +02:00 [INF] Hosting environment: Development
2025-05-25 22:44:07.748 +02:00 [INF] Content root path: /Users/<USER>/Grunder/RealEstate/RealEstateVisningAppApi/RealEstateVisningApp.HangfireApplication
2025-05-25 22:44:07.756 +02:00 [INF] Server nomll7y29f004:89460:319a7454 successfully announced in 2.8464 ms
2025-05-25 22:44:07.757 +02:00 [DBG] Execution loop ServerHeartbeatProcess:3421552a has started in 1.0902 ms
2025-05-25 22:44:07.758 +02:00 [INF] Server nomll7y29f004:89460:319a7454 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-05-25 22:44:07.784 +02:00 [DBG] Execution loop ServerWatchdog:a5d039f5 has started in 25.8761 ms
2025-05-25 22:44:07.784 +02:00 [DBG] Execution loop ServerJobCancellationWatcher:3e3bde87 has started in 25.8078 ms
2025-05-25 22:44:07.806 +02:00 [DBG] Execution loop ExpirationManager:951443c5 has started in 21.9272 ms
2025-05-25 22:44:07.806 +02:00 [DBG] Execution loop CountersAggregator:1e341745 has started in 21.807 ms
2025-05-25 22:44:07.832 +02:00 [DBG] Execution loop Worker:ab2f9d41 has started in 47.4947 ms
2025-05-25 22:44:07.857 +02:00 [DBG] Execution loop Worker:da925f98 has started in 72.6041 ms
2025-05-25 22:44:07.881 +02:00 [DBG] Execution loop Worker:02948e47 has started in 95.9791 ms
2025-05-25 22:44:07.881 +02:00 [DBG] Execution loop Worker:7416b5b7 has started in 95.9455 ms
2025-05-25 22:44:07.926 +02:00 [DBG] Execution loop Worker:5afa5615 has started in 119.2837 ms
2025-05-25 22:44:07.926 +02:00 [DBG] Execution loop Worker:ceaeff1b has started in 141.3707 ms
2025-05-25 22:44:07.926 +02:00 [DBG] Execution loop Worker:405b9a5d has started in 141.4403 ms
2025-05-25 22:44:07.926 +02:00 [DBG] Execution loop Worker:c3803441 has started in 141.5823 ms
2025-05-25 22:44:07.951 +02:00 [DBG] Execution loop Worker:82ab514f has started in 166.711 ms
2025-05-25 22:44:07.951 +02:00 [DBG] Execution loop Worker:be6275ea has started in 166.805 ms
2025-05-25 22:44:07.951 +02:00 [DBG] Execution loop Worker:89b001fb has started in 166.8385 ms
2025-05-25 22:44:07.979 +02:00 [DBG] Execution loop Worker:9fe1eed4 has started in 193.9505 ms
2025-05-25 22:44:07.979 +02:00 [DBG] Execution loop Worker:78fb80cd has started in 194.0112 ms
2025-05-25 22:44:07.979 +02:00 [DBG] Execution loop Worker:3831648f has started in 194.4458 ms
2025-05-25 22:44:08.005 +02:00 [DBG] Execution loop Worker:5fa6caf6 has started in 219.9198 ms
2025-05-25 22:44:08.005 +02:00 [DBG] Execution loop Worker:109f91a9 has started in 219.9601 ms
2025-05-25 22:44:08.081 +02:00 [DBG] Execution loop Worker:93b17fef has started in 296.0861 ms
2025-05-25 22:44:08.082 +02:00 [DBG] Execution loop Worker:fba4ebdb has started in 296.2372 ms
2025-05-25 22:44:08.082 +02:00 [DBG] Execution loop Worker:b6bca349 has started in 297.1236 ms
2025-05-25 22:44:08.084 +02:00 [DBG] Execution loop Worker:52b73774 has started in 299.2017 ms
2025-05-25 22:44:08.086 +02:00 [DBG] Execution loop DelayedJobScheduler:a38bbee3 has started in 4.753 ms
2025-05-25 22:44:08.086 +02:00 [INF] Server nomll7y29f004:89460:319a7454 all the dispatchers started
2025-05-25 22:44:08.087 +02:00 [DBG] Execution loop RecurringJobScheduler:83868e5f has started in 2.0429 ms
2025-05-25 22:44:09.417 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:44:09.433 +02:00 [WRN] Failed to determine the https port for redirect.
2025-05-25 22:44:09.439 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.479 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.480 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire - 200 null text/html 62.9895ms
2025-05-25 22:44:09.514 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/js18140206415877 - null null
2025-05-25 22:44:09.516 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css-dark181401752054329 - null null
2025-05-25 22:44:09.516 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css181401199747735 - null null
2025-05-25 22:44:09.516 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.516 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.517 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.519 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.519 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css-dark181401752054329 - 200 null text/css 2.8253ms
2025-05-25 22:44:09.526 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.526 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css181401199747735 - 200 null text/css 9.5839ms
2025-05-25 22:44:09.535 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:09.537 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/js18140206415877 - 200 null application/javascript 22.6594ms
2025-05-25 22:44:10.760 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:44:10.761 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:10.801 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:10.801 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 41.4826ms
2025-05-25 22:44:12.845 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:44:12.848 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:12.872 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:12.873 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 27.5613ms
2025-05-25 22:44:13.287 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - application/x-www-form-urlencoded; charset=UTF-8 30
2025-05-25 22:44:13.289 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:13.330 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:13.331 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - 204 null null 43.2981ms
2025-05-25 22:44:13.340 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:44:13.341 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:13.343 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:13.371 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 30.0945ms
2025-05-25 22:44:16.859 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:44:16.860 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:16.866 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:16.875 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 15.3537ms
2025-05-25 22:44:18.116 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:44:18.116 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:18.117 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:18.117 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 1.2817ms
2025-05-25 22:44:18.878 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:44:18.879 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:44:18.879 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:44:18.879 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 0.8846ms
2025-05-25 22:47:20.632 +02:00 [INF] Appsettings Environment Development
2025-05-25 22:47:20.996 +02:00 [INF] Now listening on: http://localhost:5208
2025-05-25 22:47:21.000 +02:00 [INF] Starting Hangfire Server using job storage: 'Hangfire.MemoryStorage.MemoryStorage'
2025-05-25 22:47:21.001 +02:00 [INF] Using the following options for Hangfire Server:
    Worker count: 20
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-05-25 22:47:21.013 +02:00 [DBG] Execution loop BackgroundServerProcess:a57be057 has started in 2.0719 ms
2025-05-25 22:47:21.014 +02:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-25 22:47:21.014 +02:00 [INF] Hosting environment: Development
2025-05-25 22:47:21.014 +02:00 [INF] Content root path: /Users/<USER>/Grunder/RealEstate/RealEstateVisningAppApi/RealEstateVisningApp.HangfireApplication
2025-05-25 22:47:21.022 +02:00 [INF] Server nomll7y29f004:90615:8f62689f successfully announced in 3.3811 ms
2025-05-25 22:47:21.024 +02:00 [DBG] Execution loop ServerHeartbeatProcess:c2aa6598 has started in 1.6298 ms
2025-05-25 22:47:21.025 +02:00 [INF] Server nomll7y29f004:90615:8f62689f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-05-25 22:47:21.048 +02:00 [DBG] Execution loop ServerWatchdog:a2a3245a has started in 23.1427 ms
2025-05-25 22:47:21.048 +02:00 [DBG] Execution loop ServerJobCancellationWatcher:36c90102 has started in 23.122 ms
2025-05-25 22:47:21.074 +02:00 [DBG] Execution loop ExpirationManager:3650c8e7 has started in 26.3454 ms
2025-05-25 22:47:21.074 +02:00 [DBG] Execution loop CountersAggregator:b7f4cb8f has started in 26.1915 ms
2025-05-25 22:47:21.124 +02:00 [DBG] Execution loop Worker:ce169319 has started in 74.5343 ms
2025-05-25 22:47:21.149 +02:00 [DBG] Execution loop Worker:6bba9954 has started in 100.1178 ms
2025-05-25 22:47:21.124 +02:00 [DBG] Execution loop Worker:8abe2dfa has started in 74.6633 ms
2025-05-25 22:47:21.149 +02:00 [DBG] Execution loop Worker:732fee5a has started in 100.2076 ms
2025-05-25 22:47:21.175 +02:00 [DBG] Execution loop Worker:00ffbf41 has started in 125.7012 ms
2025-05-25 22:47:21.175 +02:00 [DBG] Execution loop Worker:ff827133 has started in 125.7905 ms
2025-05-25 22:47:21.175 +02:00 [DBG] Execution loop Worker:ec69689a has started in 125.716 ms
2025-05-25 22:47:21.176 +02:00 [DBG] Execution loop Worker:1f0e0318 has started in 126.9168 ms
2025-05-25 22:47:21.199 +02:00 [DBG] Execution loop Worker:a5acfe02 has started in 149.8009 ms
2025-05-25 22:47:21.199 +02:00 [DBG] Execution loop Worker:e310e387 has started in 149.8497 ms
2025-05-25 22:47:21.246 +02:00 [DBG] Execution loop Worker:835dc903 has started in 196.6264 ms
2025-05-25 22:47:21.246 +02:00 [DBG] Execution loop Worker:821ea118 has started in 196.7202 ms
2025-05-25 22:47:21.247 +02:00 [DBG] Execution loop Worker:ebececba has started in 198.2262 ms
2025-05-25 22:47:21.273 +02:00 [DBG] Execution loop Worker:f7bda02e has started in 223.9197 ms
2025-05-25 22:47:21.299 +02:00 [DBG] Execution loop Worker:17e7c7b2 has started in 249.6643 ms
2025-05-25 22:47:21.300 +02:00 [DBG] Execution loop Worker:a9164dda has started in 250.5432 ms
2025-05-25 22:47:21.346 +02:00 [DBG] Execution loop Worker:7f149c10 has started in 297.0121 ms
2025-05-25 22:47:21.320 +02:00 [DBG] Execution loop Worker:24d8de73 has started in 271.062 ms
2025-05-25 22:47:21.372 +02:00 [DBG] Execution loop Worker:42b28d94 has started in 322.9782 ms
2025-05-25 22:47:21.373 +02:00 [DBG] Execution loop Worker:644b8cee has started in 323.7485 ms
2025-05-25 22:47:21.376 +02:00 [DBG] Execution loop DelayedJobScheduler:d21c4ce7 has started in 30.0904 ms
2025-05-25 22:47:21.376 +02:00 [INF] Server nomll7y29f004:90615:8f62689f all the dispatchers started
2025-05-25 22:47:21.377 +02:00 [DBG] Execution loop RecurringJobScheduler:675dd41c has started in 1.3971 ms
2025-05-25 22:47:21.521 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire - null null
2025-05-25 22:47:21.536 +02:00 [WRN] Failed to determine the https port for redirect.
2025-05-25 22:47:21.543 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.582 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.583 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire - 200 null text/html 62.6872ms
2025-05-25 22:47:21.591 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/js18140347007649 - null null
2025-05-25 22:47:21.591 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css-dark181401544187591 - null null
2025-05-25 22:47:21.591 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/css18140210181112 - null null
2025-05-25 22:47:21.591 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.591 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.591 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.594 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.594 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css-dark181401544187591 - 200 null text/css 3.34ms
2025-05-25 22:47:21.614 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.614 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:21.616 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/css18140210181112 - 200 null text/css 24.844ms
2025-05-25 22:47:21.616 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/js18140347007649 - 200 null application/javascript 25.3377ms
2025-05-25 22:47:23.427 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:47:23.429 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:23.464 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:23.464 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 38.2598ms
2025-05-25 22:47:25.505 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:47:25.508 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:25.530 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:25.531 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 25.5217ms
2025-05-25 22:47:27.133 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - application/x-www-form-urlencoded; charset=UTF-8 30
2025-05-25 22:47:27.135 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:27.179 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:27.179 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/recurring/trigger - 204 null null 45.4248ms
2025-05-25 22:47:27.187 +02:00 [INF] Request starting HTTP/1.1 GET http://localhost:5208/hangfire/recurring - null null
2025-05-25 22:47:27.187 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:27.188 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:27.188 +02:00 [INF] Request finished HTTP/1.1 GET http://localhost:5208/hangfire/recurring - 200 null text/html 1.9034ms
2025-05-25 22:47:29.947 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:47:29.947 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:29.988 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:29.988 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 40.9824ms
2025-05-25 22:47:31.997 +02:00 [INF] Request starting HTTP/1.1 POST http://localhost:5208/hangfire/stats - application/x-www-form-urlencoded; charset=UTF-8 167
2025-05-25 22:47:31.999 +02:00 [INF] Executing endpoint '/hangfire/{**path}'
2025-05-25 22:47:32.001 +02:00 [INF] Executed endpoint '/hangfire/{**path}'
2025-05-25 22:47:32.002 +02:00 [INF] Request finished HTTP/1.1 POST http://localhost:5208/hangfire/stats - 200 null application/json 4.4047ms
